import { Table } from '@pulumi/aws/dynamodb';
import { Function } from '@pulumi/aws/lambda';
import { sqsBatchWindowMaximum } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { addLambdaInvokePolicyToRole, createLambdaFunction } from '../lambda';
import {
  addLambdaPermissionForSQS,
  addQueueReadPolicyToRole,
  createDLQ,
  createQueue,
  createQueuePolicy,
  subscribeQueueToTopic,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';
import { addTopicPolicyToRole, createTopic } from '../topic';

export function createScoutingResources({
  scoutingRequestsTable,
  scoutedPlayersTable,
  managersTable,
  teamsTable,
  playersTable,
  getManagerLambda,
}: {
  scoutingRequestsTable: Table;
  scoutedPlayersTable: Table;
  managersTable: Table;
  teamsTable: Table;
  playersTable: Table;
  getManagerLambda: Function;
}) {
  // Create SNS Topic for scouting
  const scoutingTopic = createTopic('scoutingTopic');

  // Create SQS Queues and DLQs for each type
  const playerScoutingDLQ = createDLQ('playerScoutingQueue');
  const playerScoutingQueue = createQueue('playerScoutingQueue', playerScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  const teamScoutingDLQ = createDLQ('teamScoutingQueue');
  const teamScoutingQueue = createQueue('teamScoutingQueue', teamScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  const leagueScoutingDLQ = createDLQ('leagueScoutingQueue');
  const leagueScoutingQueue = createQueue('leagueScoutingQueue', leagueScoutingDLQ, 3, {
    visibilityTimeout: 300,
  });

  // Create queue policies and subscribe to SNS topic with filters
  createQueuePolicy('playerScoutingQueue', playerScoutingQueue, scoutingTopic.arn);
  createQueuePolicy('teamScoutingQueue', teamScoutingQueue, scoutingTopic.arn);
  createQueuePolicy('leagueScoutingQueue', leagueScoutingQueue, scoutingTopic.arn);

  const playerQueueRole = addQueueReadPolicyToRole('playerScoutingQueue', playerScoutingQueue);
  const teamQueueRole = addQueueReadPolicyToRole('teamScoutingQueue', teamScoutingQueue);
  const leagueQueueRole = addQueueReadPolicyToRole('leagueScoutingQueue', leagueScoutingQueue);

  // Subscribe queues to topic with type-specific filters
  subscribeQueueToTopic('playerScoutingQueue', playerScoutingQueue, scoutingTopic, {
    DataType: ['player'],
  });
  subscribeQueueToTopic('teamScoutingQueue', teamScoutingQueue, scoutingTopic, {
    DataType: ['team'],
  });
  subscribeQueueToTopic('leagueScoutingQueue', leagueScoutingQueue, scoutingTopic, {
    DataType: ['league'],
  });

  // Create the queueScoutingRequests Lambda role with DynamoDB permissions
  let queueScoutingRequestsRole = addDynamoPolicyToRole(
    'queueScoutingRequestsHandler',
    [scoutingRequestsTable],
    ['dynamodb:Scan', 'dynamodb:DeleteItem']
  );

  // Add SNS publish permissions to the role
  queueScoutingRequestsRole = addTopicPolicyToRole(
    'queueScoutingRequestsHandler',
    scoutingTopic,
    queueScoutingRequestsRole
  );

  // Create the Lambda function for queueing requests
  const [queueScoutingRequestsLambda] = createLambdaFunction(
    'queueScoutingRequestsHandler',
    '../dist/scouting/queueScoutingRequests',
    'index.handler',
    {
      SCOUTING_REQUESTS_TABLE_NAME: scoutingRequestsTable.name,
      SCOUTING_TOPIC_ARN: scoutingTopic.arn,
    },
    queueScoutingRequestsRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  // Create type-specific processing lambdas with updated permissions
  const processPlayerScoutingRole = addDynamoPolicyToRole(
    'processPlayerScoutingHandler',
    [scoutingRequestsTable, scoutedPlayersTable, playersTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem', 'dynamodb:PutItem'],
    playerQueueRole
  );

  const processTeamScoutingRole = addDynamoPolicyToRole(
    'processTeamScoutingHandler',
    [scoutingRequestsTable, scoutedPlayersTable, playersTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem', 'dynamodb:PutItem', 'dynamodb:Query'],
    teamQueueRole
  );

  const processLeagueScoutingRole = addDynamoPolicyToRole(
    'processLeagueScoutingHandler',
    [scoutingRequestsTable, scoutedPlayersTable, playersTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem', 'dynamodb:PutItem', 'dynamodb:Query'],
    leagueQueueRole
  );

  const [processPlayerScoutingLambda, processPlayerScoutingLogGroup] = createLambdaFunction(
    'processPlayerScoutingHandler',
    '../dist/scouting/processPlayerScouting',
    'index.handler',
    {
      SCOUTING_REQUESTS_TABLE_NAME: scoutingRequestsTable.name,
      SCOUTED_PLAYERS_TABLE_NAME: scoutedPlayersTable.name,
      PLAYERS_TABLE_NAME: playersTable.name,
      QUEUE_URL: playerScoutingQueue.url,
    },
    processPlayerScoutingRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  const [processTeamScoutingLambda, processTeamScoutingLogGroup] = createLambdaFunction(
    'processTeamScoutingHandler',
    '../dist/scouting/processTeamScouting',
    'index.handler',
    {
      SCOUTING_REQUESTS_TABLE_NAME: scoutingRequestsTable.name,
      SCOUTED_PLAYERS_TABLE_NAME: scoutedPlayersTable.name,
      PLAYERS_TABLE_NAME: playersTable.name,
      QUEUE_URL: teamScoutingQueue.url,
    },
    processTeamScoutingRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  const [processLeagueScoutingLambda, processLeagueScoutingLogGroup] = createLambdaFunction(
    'processLeagueScoutingHandler',
    '../dist/scouting/processLeagueScouting',
    'index.handler',
    {
      SCOUTING_REQUESTS_TABLE_NAME: scoutingRequestsTable.name,
      SCOUTED_PLAYERS_TABLE_NAME: scoutedPlayersTable.name,
      PLAYERS_TABLE_NAME: playersTable.name,
      QUEUE_URL: leagueScoutingQueue.url,
    },
    processLeagueScoutingRole,
    undefined,
    {
      memorySize: 512,
      timeout: 300,
    }
  );

  // Add Lambda permissions and event source mappings
  addLambdaPermissionForSQS(
    'processPlayerScoutingHandler',
    processPlayerScoutingLambda,
    playerScoutingQueue
  );
  addLambdaPermissionForSQS(
    'processTeamScoutingHandler',
    processTeamScoutingLambda,
    teamScoutingQueue
  );
  addLambdaPermissionForSQS(
    'processLeagueScoutingHandler',
    processLeagueScoutingLambda,
    leagueScoutingQueue
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('scouting-error');

  // Create monitored event source mappings that will alarm on DLQ messages
  createMonitoredEventSourceMapping(
    'processPlayerScoutingHandler',
    processPlayerScoutingLambda,
    playerScoutingQueue,
    playerScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );
  createMonitoredEventSourceMapping(
    'processTeamScoutingHandler',
    processTeamScoutingLambda,
    teamScoutingQueue,
    teamScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );
  createMonitoredEventSourceMapping(
    'processLeagueScoutingHandler',
    processLeagueScoutingLambda,
    leagueScoutingQueue,
    leagueScoutingDLQ,
    10,
    sqsBatchWindowMaximum,
    undefined,
    [errorAlarmTopic.arn]
  );

  createScheduledRule({
    name: 'queue-scouting-requests',
    description: 'Trigger scouting requests queueing every 2 hours',
    scheduleExpression: 'rate(2 hours)',
    lambda: queueScoutingRequestsLambda,
  });

  let requestScoutingRole = addDynamoPolicyToRole(
    'requestScoutingHandler',
    [managersTable, teamsTable, scoutingRequestsTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem', 'dynamodb:PutItem']
  );

  const [requestScoutingLambda] = createLambdaFunction(
    'requestScoutingHandler',
    '../dist/scouting/requestScouting',
    'index.handler',
    {
      MANAGERS_TABLE_NAME: managersTable.name,
      TEAMS_TABLE_NAME: teamsTable.name,
      SCOUTING_REQUESTS_TABLE_NAME: scoutingRequestsTable.name,
    },
    requestScoutingRole
  );

  // Create Lambda for getting scouted players
  let getScoutedPlayersRole = addDynamoPolicyToRole(
    'getScoutedPlayersHandler',
    [scoutedPlayersTable, playersTable, managersTable],
    ['dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:BatchGetItem']
  );

  // Add permission to invoke the getManager Lambda
  getScoutedPlayersRole = addLambdaInvokePolicyToRole(
    'getScoutedPlayersHandler',
    [getManagerLambda],
    getScoutedPlayersRole
  );

  const [getScoutedPlayersLambda] = createLambdaFunction(
    'getScoutedPlayersHandler',
    '../dist/scouting/getScoutedPlayers',
    'index.handler',
    {
      SCOUTED_PLAYERS_TABLE_NAME: scoutedPlayersTable.name,
      PLAYERS_TABLE_NAME: playersTable.name,
      MANAGERS_TABLE_NAME: managersTable.name,
    },
    getScoutedPlayersRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  return {
    scoutingTopic,
    playerScoutingQueue,
    teamScoutingQueue,
    leagueScoutingQueue,
    requestScoutingLambda,
    queueScoutingRequestsLambda,
    processPlayerScoutingLambda,
    processTeamScoutingLambda,
    processLeagueScoutingLambda,
    getScoutedPlayersLambda,
    errorAlarmTopic,
  };
}
