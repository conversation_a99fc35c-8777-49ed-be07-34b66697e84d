import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindowMaximum, sqsBatchWindowMinimum } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createLambdaFunction } from '../lambda';
import { addLambdaPermissionForSQS, addQueueReadPolicyToRole } from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

export interface PlayerResourcesConfig {
  playersTable: any;
  transferListedPlayersTable: any;
  playerQueue: Queue;
  playerDLQ: Queue;
  unattachedPlayersQueue: Queue;
  unattachedPlayersDLQ: Queue;
}

export function createPlayerResources(config: PlayerResourcesConfig) {
  let playerQueueRole = addQueueReadPolicyToRole('playerQueue', config.playerQueue);

  let playerRole = addDynamoPolicyToRole(
    'playerHandler',
    [config.playersTable],
    ['dynamodb:PutItem', 'dynamodb:BatchWriteItem'],
    playerQueueRole
  );

  const [playerLambda, playerLogGroup] = createLambdaFunction(
    'playerHandler',
    '../dist/generate/player',
    'index.handler',
    {
      QUEUE_URL: config.playerQueue.url,
      PLAYERS_TABLE_NAME: config.playersTable.name,
    },
    playerRole,
    undefined,
    {
      memorySize: 256,
      timeout: 60,
    }
  );

  addLambdaPermissionForSQS('playerHandler', playerLambda, config.playerQueue);

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('player-error');

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'playerHandler',
    playerLambda,
    config.playerQueue,
    config.playerDLQ,
    10, // batchSize
    sqsBatchWindowMinimum, // maximumBatchingWindowInSeconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    }, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  const unattachedPlayersQueueRole = addQueueReadPolicyToRole(
    'unattachedPlayersQueue',
    config.unattachedPlayersQueue
  );

  let unattachedPlayersRole = addDynamoPolicyToRole(
    'unattachedPlayersHandler',
    [config.transferListedPlayersTable],
    ['dynamodb:PutItem', 'dynamodb:BatchWriteItem'],
    unattachedPlayersQueueRole
  );

  const [unattachedPlayersLambda, unattachedPlayersLogGroup] = createLambdaFunction(
    'unattachedPlayersHandler',
    '../dist/generate/unattached-players',
    'index.handler',
    {
      QUEUE_URL: config.unattachedPlayersQueue.url,
      TRANSFER_LISTED_PLAYERS_TABLE_NAME: config.transferListedPlayersTable.name,
    },
    unattachedPlayersRole
  );

  addLambdaPermissionForSQS(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue
  );

  // Add SQS Queue Event Source Mapping to Lambda with monitoring
  createMonitoredEventSourceMapping(
    'unattachedPlayersHandler',
    unattachedPlayersLambda,
    config.unattachedPlayersQueue,
    config.unattachedPlayersDLQ,
    10, // batchSize
    sqsBatchWindowMaximum, // maximumBatchingWindowInSeconds
    undefined, // additionalConfig
    [errorAlarmTopic.arn] // alarmActions
  );

  // Transfer list players API
  let getTransferListPlayersRole = addDynamoPolicyToRole(
    'getTransferListPlayersHandler',
    [config.transferListedPlayersTable],
    ['dynamodb:Query']
  );

  const [getTransferListPlayersLambda] = createLambdaFunction(
    'getTransferListPlayersHandler',
    '../dist/player/getTransferListPlayers',
    'index.handler',
    {
      TRANSFER_LISTED_PLAYERS_TABLE_NAME: config.transferListedPlayersTable.name,
    },
    getTransferListPlayersRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // My Bid Transfer List Players API
  let getMyBidTransferListPlayersRole = addDynamoPolicyToRole(
    'getMyBidTransferListPlayersHandler',
    [config.transferListedPlayersTable],
    ['dynamodb:Query']
  );

  const [getMyBidTransferListPlayersLambda] = createLambdaFunction(
    'getMyBidTransferListPlayersHandler',
    '../dist/player/getMyBidTransferListPlayers',
    'index.handler',
    {
      TRANSFER_LISTED_PLAYERS_TABLE_NAME: config.transferListedPlayersTable.name,
    },
    getMyBidTransferListPlayersRole
  );

  const [useMagicSpongeLambda] = createLambdaFunction(
    'useMagicSpongeHandler',
    '../dist/player/useMagicSponge',
    'index.handler'
  );

  return {
    playerLambda,
    unattachedPlayersLambda,
    getTransferListPlayersLambda,
    getMyBidTransferListPlayersLambda,
    useMagicSpongeLambda,
    errorAlarmTopic,
  };
}
