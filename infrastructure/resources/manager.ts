import { sqsBatchWindowMinimum } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createLambdaFunction } from '../lambda';
import {
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createEventSourceMapping,
  createQueue,
} from '../queue';

export interface ManagerResourcesConfig {
  managersTable: any;
  availableTeamsTable: any;
}

export function createManagerResources(config: ManagerResourcesConfig) {
  // Create manager queue
  const createManagerDlq = createDLQ('createManagerDLQ');
  const createManagerQueue = createQueue('createManager', createManagerDlq);

  // Post confirmation Lambda
  let postConfirmationRole = addDynamoPolicyToRole(
    'postConfirmation',
    [config.managersTable, config.availableTeamsTable],
    ['dynamodb:Scan', 'dynamodb:PutItem', 'dynamodb:DeleteItem']
  );

  postConfirmationRole = addQueueSendPolicyToRole(
    'createManagerQueueRole',
    createManagerQueue,
    postConfirmationRole
  );

  const [postConfirmationLambda] = createLambdaFunction(
    'postConfirmation',
    '../dist/manager/requestNewManagerOnSignup',
    'index.handler',
    {
      MANAGER_QUEUE_URL: createManagerQueue.url,
    },
    postConfirmationRole
  );

  const getManagerLambdaRole = addDynamoPolicyToRole(
    'getManagerHandler',
    [config.managersTable],
    ['dynamodb:GetItem']
  );

  const [getManagerLambda] = createLambdaFunction(
    'getManagerHandler',
    '../dist/manager/getManager',
    'index.handler',
    {
      MANAGERS_TABLE_NAME: config.managersTable.name,
    },
    getManagerLambdaRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create manager Lambda to process queue messages
  let createManagerLambdaRole = addDynamoPolicyToRole(
    'createManagerHandler',
    [config.managersTable, config.availableTeamsTable],
    ['dynamodb:GetItem', 'dynamodb:PutItem', 'dynamodb:DeleteItem', 'dynamodb:Scan']
  );

  createManagerLambdaRole = addQueueReadPolicyToRole(
    'createManagerQueueRole',
    createManagerQueue,
    createManagerLambdaRole
  );

  const [createManagerLambda] = createLambdaFunction(
    'createManagerHandler',
    '../dist/manager/createAndAssignNewManager',
    'index.handler',
    {
      MANAGERS_TABLE_NAME: config.managersTable.name,
      AVAILABLE_TEAMS_TABLE_NAME: config.availableTeamsTable.name,
    },
    createManagerLambdaRole
  );

  // Create event source mapping to connect the Lambda to the queue
  const createManagerEventSourceMapping = createEventSourceMapping(
    'createManager',
    createManagerLambda,
    createManagerQueue,
    10, // batchSize
    sqsBatchWindowMinimum
  );

  // Create update manager name Lambda
  const updateManagerNameLambdaRole = addDynamoPolicyToRole(
    'updateManagerNameHandler',
    [config.managersTable],
    ['dynamodb:GetItem', 'dynamodb:UpdateItem']
  );

  const [updateManagerNameLambda] = createLambdaFunction(
    'updateManagerNameHandler',
    '../dist/manager/updateManagerName',
    'index.handler',
    {
      MANAGERS_TABLE_NAME: config.managersTable.name,
    },
    updateManagerNameLambdaRole
  );

  const [updateNotificationPreferencesLambda] = createLambdaFunction(
    'updateNotificationPreferencesHandler',
    '../dist/manager/updateNotificationPreferences',
    'index.handler'
  );

  // Create get inbox messages Lambda
  const getInboxMessagesLambdaRole = addDynamoPolicyToRole(
    'getInboxMessagesHandler',
    [config.managersTable],
    ['dynamodb:GetItem']
  );

  const [getInboxMessagesLambda] = createLambdaFunction(
    'getInboxMessagesHandler',
    '../dist/inbox/getInboxMessages',
    'index.handler',
    {
      MANAGERS_TABLE_NAME: config.managersTable.name,
    },
    getInboxMessagesLambdaRole
  );

  const [getRewardsLambda] = createLambdaFunction(
    'getRewardsHandler',
    '../dist/manager/dailyReward',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  return {
    postConfirmationLambda,
    getManagerLambda,
    createManagerLambda,
    createManagerEventSourceMapping,
    updateManagerNameLambda,
    updateNotificationPreferencesLambda,
    getInboxMessagesLambda,
    getRewardsLambda,
  };
}
