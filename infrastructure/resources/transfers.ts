import { Queue } from '@pulumi/aws/sqs';
import { sqsBatchWindowMaximum, stageName } from '../config';
import { addDynamoPolicyToRole } from '../dynamodb';
import { createScheduledRule } from '../eventBridge';
import { createLambdaFunction } from '../lambda';
import {
  addQueueReadPolicyToRole,
  addQueueSendPolicyToRole,
  createDLQ,
  createQueue,
} from '../queue';
import { createCloudWatchAlarmTopic, createMonitoredEventSourceMapping } from '../queueMonitoring';

interface TransferResourcesConfig {
  transferListedPlayersTable: any;
  emailQueue: Queue;
  unattachedPlayersQueue: Queue;
}

export function createTransferResources(config: TransferResourcesConfig) {
  // Create dead letter queue for AI transfers
  const aiTransfersDLQ = createDLQ('ai-transfers');

  // Create main queue for AI transfers
  const aiTransfersQueue = createQueue('ai-transfers', aiTransfersDLQ, 3, {
    visibilityTimeout: 120, // 2 minutes
    messageRetentionSeconds: 86400, // 1 day
  });

  const [submitTransferOfferLambda] = createLambdaFunction(
    'submitTransferOfferHandler',
    '../dist/transfers/submitOffer',
    'index.handler'
  );

  const [submitBidLambda] = createLambdaFunction(
    'submitBidHandler',
    '../dist/transfers/submitBid',
    'index.handler'
  );

  const [myActiveTransfersLambda] = createLambdaFunction(
    'myActiveTransfersHandler',
    '../dist/transfers/myActiveTransfers',
    'index.handler'
  );

  const [acceptTransferRequestLambda] = createLambdaFunction(
    'acceptTransferRequestHandler',
    '../dist/transfers/acceptTransferRequest',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 256,
      timeout: 45,
    }
  );

  const [cancelTransferRequestLambda] = createLambdaFunction(
    'cancelTransferRequestHandler',
    '../dist/transfers/cancelTransferRequest',
    'index.handler'
  );

  const [releasePlayerLambda] = createLambdaFunction(
    'releasePlayerHandler',
    '../dist/transfers/releasePlayer',
    'index.handler'
  );

  // Create a role with permissions to access the transfer listed players table
  let processAuctionEndRole = addDynamoPolicyToRole(
    'processAuctionEnd',
    [config.transferListedPlayersTable],
    ['dynamodb:GetItem', 'dynamodb:Query', 'dynamodb:UpdateItem', 'dynamodb:DeleteItem']
  );

  // Add permission to send messages to the unattached players queue
  // Note: Email queue permission is handled globally
  processAuctionEndRole = addQueueSendPolicyToRole(
    'processAuctionEndToUnattachedPlayersQueue',
    config.unattachedPlayersQueue,
    processAuctionEndRole
  );

  // Create an SNS topic for CloudWatch alarms
  const errorAlarmTopic = createCloudWatchAlarmTopic('transfer-error');

  // Create the Lambda function for processing auction ends
  const [processAuctionEndLambda, processAuctionEndLogGroup] = createLambdaFunction(
    'processAuctionEndHandler',
    '../dist/transfers/processAuctionEnd',
    'index.handler',
    {
      // EMAIL_QUEUE_URL is now handled globally
      UNATTACHED_PLAYERS_QUEUE_URL: config.unattachedPlayersQueue.url,
    },
    processAuctionEndRole,
    undefined,
    {
      memorySize: 384,
      timeout: 200,
    }
  );

  // Create EventBridge rule to trigger the lambda at midnight UK time
  createScheduledRule({
    name: 'process-auction-end',
    description: 'Triggers a lambda to process completed auctions at midnight',
    scheduleExpression: 'cron(30 * * * ? *)', // Half past every hour
    lambda: processAuctionEndLambda,
  });

  // Create Lambda for simulating AI transfers
  let simulateAITransfersRole = addQueueSendPolicyToRole('simulateAITransfers', aiTransfersQueue);

  const [simulateAITransfersLambda] = createLambdaFunction(
    'simulateAITransfersHandler',
    '../dist/transfers/simulateAITransfers',
    'index.handler',
    {
      AI_TRANSFERS_QUEUE_URL: aiTransfersQueue.url,
    },
    simulateAITransfersRole,
    undefined,
    {
      memorySize: 256,
      timeout: 30,
    }
  );

  // Create scheduled rule to trigger AI transfers simulation
  createScheduledRule({
    name: 'simulate-ai-transfers',
    description: 'Triggers a lambda to simulate AI transfers',
    scheduleExpression: 'cron(0 * * * ? *)', // Every hour
    lambda: simulateAITransfersLambda,
  });

  // Create Lambda for processing AI transfer requests from the queue
  let processAITransfersRole = addQueueReadPolicyToRole('processAITransfers', aiTransfersQueue);

  // Create the Lambda function for processing AI transfers from the queue
  const [processAITransfersLambda, processAITransfersLogGroup] = createLambdaFunction(
    'processAITransfersHandler',
    '../dist/transfers/processAITransfers',
    'index.handler',
    {},
    processAITransfersRole,
    undefined,
    {
      memorySize: 512,
      timeout: 120,
    }
  );

  // Create monitored event source mapping to connect the queue to the lambda
  createMonitoredEventSourceMapping(
    'process-ai-transfers',
    processAITransfersLambda,
    aiTransfersQueue,
    aiTransfersDLQ,
    10, // process 10 messages at a time
    sqsBatchWindowMaximum, // batch window of 30 seconds
    {
      functionResponseTypes: ['ReportBatchItemFailures'],
    },
    [errorAlarmTopic.arn]
  );

  // Create Lambda for responding to transfer offers
  const [respondToTransferOffersLambda, respondToTransferOffersLogGroup] = createLambdaFunction(
    'respondToTransferOffersHandler',
    '../dist/transfers/respondToTransferOffers',
    'index.handler',
    undefined,
    undefined,
    undefined,
    {
      memorySize: 512,
      timeout: 120,
    }
  );

  // Create scheduled rule to trigger the respond to transfer offers lambda every hour
  createScheduledRule({
    name: 'respond-to-transfer-offers',
    description: 'Triggers a lambda to respond to transfer offers every hour',
    scheduleExpression: 'cron(0 * * * ? *)', // Every hour
    lambda: respondToTransferOffersLambda,
  });

  // Note: DLQ monitoring is now handled by createMonitoredEventSourceMapping
  // Individual error log alarms have been removed to reduce CloudWatch costs

  return {
    submitTransferOfferLambda,
    submitBidLambda,
    myActiveTransfersLambda,
    acceptTransferRequestLambda,
    cancelTransferRequestLambda,
    releasePlayerLambda,
    processAuctionEndLambda,
    simulateAITransfersLambda,
    processAITransfersLambda,
    respondToTransferOffersLambda,
    aiTransfersQueue,
    aiTransfersDLQ,
    errorAlarmTopic,
  };
}
