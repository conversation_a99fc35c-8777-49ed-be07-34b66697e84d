import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import {
  BidHistoryResponse,
  TransferListPathParameters as PathParameters,
  TransferListQueryParameters as QueryParameters,
  TransferListedPlayerResponse,
  TransferListResponse,
} from '@/model/transfer-list.js';
import { extractCurrentAttributes } from '@/utils/attributeUtils.js';
import { buildResponse } from '@/utils/buildResponse.js';

const main = async function (event: HttpEvent<void, PathParameters, QueryParameters>) {
  const { transferRepository } = event.context.repositories;

  const limit = event.queryStringParameters?.limit
    ? parseInt(event.queryStringParameters.limit, 10)
    : 25;

  const gameworldId = event.pathParameters.gameworldId;

  // Query the transfer listed players
  const result = await transferRepository.getTransferListedPlayers(
    gameworldId,
    limit,
    event.queryStringParameters?.lastEvaluatedKey
  );

  if (!result || result.length === 0) {
    return buildResponse(404, JSON.stringify({ error: 'No players found' }));
  }

  // Transform players to include only current attributes
  const transformedPlayers: TransferListedPlayerResponse[] = result.map((transferList) => {
    // Transform bid history to the response format
    const bidHistory: BidHistoryResponse[] = transferList.bidHistory.getItems().map((bid) => ({
      teamId: bid.team.teamId,
      teamName: bid.team.teamName,
      maximumBid: bid.maximumBid,
      bidTime: bid.bidTime,
    }));

    return {
      gameworldId: transferList.gameworldId,
      teamId: transferList.player.team?.teamId || '',
      leagueId: '',
      playerId: transferList.player.playerId,
      firstName: transferList.player.firstName,
      surname: transferList.player.surname,
      attributes: extractCurrentAttributes(transferList.player.attributes),
      age: transferList.player.age,
      value: transferList.player.value,
      auctionStartPrice: transferList.auctionStartPrice,
      auctionCurrentPrice: transferList.auctionCurrentPrice,
      auctionEndTime: transferList.auctionEndTime,
      bidHistory: bidHistory,
    };
  });

  const response: TransferListResponse = {
    players: transformedPlayers,
    /* FIXME: lastEvaluatedKey: result.lastEvaluatedKey
      ? Buffer.from(JSON.stringify(result.lastEvaluatedKey)).toString('base64')
      : undefined,*/
  };

  return buildResponse(200, JSON.stringify(response));
};

export const handler = httpMiddify(main, {});
