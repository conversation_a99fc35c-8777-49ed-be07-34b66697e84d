import { NotificationCategory } from '@/entities/Manager.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the SQS service
vi.mock('../sqs/sqs.ts', () => ({
  SQS: vi.fn().mockImplementation(() => ({
    send: vi.fn().mockResolvedValue(undefined),
  })),
}));

// Mock Expo
vi.mock('expo-server-sdk', () => ({
  Expo: vi.fn().mockImplementation(() => ({
    sendPushNotificationsAsync: vi.fn().mockResolvedValue([{ status: 'ok', id: 'test-id' }]),
  })),
  isExpoPushToken: vi.fn().mockReturnValue(true),
}));

describe('NotificationManager - Outbid Notifications', () => {
  let notificationManager: NotificationManager;
  let mockRepositories: any;

  beforeEach(() => {
    vi.clearAllMocks();
    notificationManager = NotificationManager.getInstance();
    
    mockRepositories = {
      inboxRepository: {
        createMessage: vi.fn().mockResolvedValue(undefined),
      },
    };

    // Mock manager with notification preferences
    const mockManager = {
      managerId: 'test-manager-id',
      gameworldId: 'test-gameworld',
      team: { teamId: 'test-team' },
      email: '<EMAIL>',
      pushToken: 'ExponentPushToken[test-token]',
      notificationPreferences: {
        [NotificationCategory.TRANSFERS]: {
          email: true,
          push: true,
        },
      },
    };

    notificationManager.assignManagerPreferences(mockManager, mockRepositories);
  });

  describe('outbidNotification', () => {
    it('should create correct notification content for outbid alert', async () => {
      const playerName = 'John Smith';
      const newAuctionPrice = 250000;
      const outbiddingTeamName = 'Manchester United';

      // Spy on the private sendNotification method
      const sendNotificationSpy = vi.spyOn(notificationManager as any, 'sendNotification');

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      expect(sendNotificationSpy).toHaveBeenCalledWith({
        subject: `You've been outbid on ${playerName}!`,
        content: `<p>Bad news! You've been outbid on ${playerName}.</p>
              <p>The auction price has increased to £${newAuctionPrice.toLocaleString()} thanks to a bid from ${outbiddingTeamName}.</p>
              <p>If you still want this player, you'll need to place a higher bid before the auction ends. Time to dig deeper into those pockets!</p>`,
        title: 'Outbid Alert!',
        category: NotificationCategory.TRANSFERS,
      });
    });

    it('should format large numbers correctly in notification', async () => {
      const playerName = 'Lionel Messi';
      const newAuctionPrice = 1500000; // 1.5 million
      const outbiddingTeamName = 'Real Madrid';

      const sendNotificationSpy = vi.spyOn(notificationManager as any, 'sendNotification');

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      const calledWith = sendNotificationSpy.mock.calls[0][0];
      expect(calledWith.content).toContain('£1,500,000');
    });

    it('should store notification in inbox', async () => {
      const playerName = 'Test Player';
      const newAuctionPrice = 100000;
      const outbiddingTeamName = 'Test Team';

      await notificationManager.outbidNotification(playerName, newAuctionPrice, outbiddingTeamName);

      expect(mockRepositories.inboxRepository.createMessage).toHaveBeenCalledWith(
        'test-gameworld',
        'test-team',
        expect.any(Number),
        expect.stringContaining('You\'ve been outbid on Test Player'),
        expect.stringContaining('Outbid Alert!')
      );
    });
  });
});
